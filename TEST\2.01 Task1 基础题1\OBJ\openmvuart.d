..\obj\openmvuart.o: ..\SYSTEM\openmv\openmvuart.c
..\obj\openmvuart.o: ..\SYSTEM\openmv\openmvuart.h
..\obj\openmvuart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\openmvuart.o: ..\USER\stm32f4xx_conf.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\openmvuart.o: ..\USER\stm32f4xx.h
..\obj\openmvuart.o: ..\CORE\core_cm4.h
..\obj\openmvuart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\openmvuart.o: ..\CORE\core_cmInstr.h
..\obj\openmvuart.o: ..\CORE\core_cmFunc.h
..\obj\openmvuart.o: ..\CORE\core_cm4_simd.h
..\obj\openmvuart.o: ..\USER\system_stm32f4xx.h
..\obj\openmvuart.o: ..\USER\stm32f4xx_conf.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\openmvuart.o: ..\SYSTEM\sys\stm32f4xx_syscfg.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\openmvuart.o: ..\FWLIB\inc\misc.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\openmvuart.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\openmvuart.o: ..\SYSTEM\sys\sys.h
..\obj\openmvuart.o: ..\SYSTEM\usart2\usart2.h
..\obj\openmvuart.o: ..\hardware\PID\PID.h
