#include "encoder.h"
#include "sys.h"
#include <math.h>
//#include "usart.h"

int encoderCnt[4];  //瞬时的读取的编码器计数值
long encoderSumCnt[4];//编码器累加的计数值
long encoderSumCntOld[4];//编码器累加的计数值
int8_t encoderDir[4];//编码器的方向位，大于0，一个方向，小于0一个方向

// dir=(TIMX->CR1 & 0x0010)>>4;     //取方向标志位
//if(dir > 0)  //向下计数     else     //向上计数
/**
* @brief TIM4 通道1通道2 正交编码器(编码器接口)-B6B7--T4:
* @param none
*/
void TIM4_MOTOR1_encoder_init(void)                      
{ 
	GPIO_InitTypeDef GPIO_InitStruct;            /*GPIO*/
	TIM_TimeBaseInitTypeDef  TIM_TimeBaseStruct; /*时基*/
	TIM_ICInitTypeDef TIM_ICInitStruct;          /*输入通道*/
	//NVIC_InitTypeDef NVIC_InitStructure;         /*中断*/
    
    /*GPIO初始化*/    
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE); /*使能GPIO时钟 AHB1*/                    
	GPIO_StructInit(&GPIO_InitStruct);        
	GPIO_InitStruct.GPIO_Pin = GPIO_Pin_6 | GPIO_Pin_7; 
	GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AF;        /*复用功能*/
	GPIO_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;	 /*速度100MHz*/
	GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;   
	GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;        
	GPIO_Init(GPIOB, &GPIO_InitStruct); 
	
	GPIO_PinAFConfig(GPIOB,GPIO_PinSource6,GPIO_AF_TIM4); 
	GPIO_PinAFConfig(GPIOB,GPIO_PinSource7,GPIO_AF_TIM4); 

	/*时基初始化*/
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM4, ENABLE);   /*使能定时器时钟 APB1*/
	TIM_DeInit(TIM4);  
	TIM_TimeBaseStructInit(&TIM_TimeBaseStruct);    
	TIM_TimeBaseStruct.TIM_Prescaler = ENCODER_TIM_PSC;       /*预分频 */        
	TIM_TimeBaseStruct.TIM_Period = ENCODER_TIM_PERIOD;       /*周期(重装载值)*/
	TIM_TimeBaseStruct.TIM_ClockDivision = TIM_CKD_DIV1;      
	TIM_TimeBaseStruct.TIM_CounterMode = TIM_CounterMode_Up;  /*连续向上计数模式*/  
	TIM_TimeBaseInit(TIM4, &TIM_TimeBaseStruct); 

	/*编码器模式配置：同时捕获通道1与通道2(即4倍频)，极性均为Rising*/
	TIM_EncoderInterfaceConfig(TIM4, TIM_EncoderMode_TI12,TIM_ICPolarity_Rising, TIM_ICPolarity_Rising); 


  TIM_ICStructInit(&TIM_ICInitStruct);        
	TIM_ICInitStruct.TIM_ICFilter = 0;   /*输入通道的滤波参数*/
	TIM_ICInit(TIM4, &TIM_ICInitStruct); /*输入通道初始化*/
	TIM_SetCounter(TIM4, CNT_INIT);      /*CNT设初值*/
	TIM_ClearFlag(TIM4,TIM_IT_Update);   /*中断标志清0*/
	TIM_ITConfig(TIM4, TIM_IT_Update, ENABLE); /*中断使能*/
	TIM_Cmd(TIM4,ENABLE);                /*使能CR寄存器*/
	
	/*中断配置*/
//	NVIC_InitStructure.NVIC_IRQChannel=TIM4_IRQn; //定时器4中断
//	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=0x00; //抢占优先级1
//	NVIC_InitStructure.NVIC_IRQChannelSubPriority=0x01; //子优先级1
//	NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
//	NVIC_Init(&NVIC_InitStructure);
} 


/**
* @brief TIM5 通道1通道2 正交编码器(编码器接口)A0   A1--T5
* @param none
*/
void TIM5_MOTOR2_encoder_init(void)                      
{ 
	GPIO_InitTypeDef GPIO_InitStruct;            /*GPIO*/
	TIM_TimeBaseInitTypeDef  TIM_TimeBaseStruct; /*时基*/
	TIM_ICInitTypeDef TIM_ICInitStruct;          /*输入通道*/
	//NVIC_InitTypeDef NVIC_InitStructure;         /*中断*/
    
    /*GPIO初始化*/    
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE); /*使能GPIO时钟 AHB1*/                    
	GPIO_StructInit(&GPIO_InitStruct);        
	GPIO_InitStruct.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1; 
	GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AF;        /*复用功能*/
	GPIO_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;	 /*速度100MHz*/
	GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;   
	GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;        
	GPIO_Init(GPIOA, &GPIO_InitStruct); 
	
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource0,GPIO_AF_TIM5); 
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource1,GPIO_AF_TIM5); 

	/*时基初始化*/
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM5, ENABLE);   /*使能定时器时钟 APB1*/
	TIM_DeInit(TIM5);  
	TIM_TimeBaseStructInit(&TIM_TimeBaseStruct);    
	TIM_TimeBaseStruct.TIM_Prescaler = ENCODER_TIM_PSC;       /*预分频 */        
	TIM_TimeBaseStruct.TIM_Period = ENCODER_TIM_PERIOD;       /*周期(重装载值)*/
	TIM_TimeBaseStruct.TIM_ClockDivision = TIM_CKD_DIV1;      
	TIM_TimeBaseStruct.TIM_CounterMode = TIM_CounterMode_Up;  /*连续向上计数模式*/  
	TIM_TimeBaseInit(TIM5, &TIM_TimeBaseStruct); 

	/*编码器模式配置：同时捕获通道1与通道2(即4倍频)，极性均为Rising......TIM_ICPolarity_Rising   TIM_ICPolarity_Falling  */
	//由于编码器线翻了，所以，将rising改为falling,其他不改。
	TIM_EncoderInterfaceConfig(TIM5, TIM_EncoderMode_TI12,TIM_ICPolarity_Falling, TIM_ICPolarity_Falling); 
	
	TIM_ICStructInit(&TIM_ICInitStruct);        
	TIM_ICInitStruct.TIM_ICFilter = 0;   /*输入通道的滤波参数*/
	TIM_ICInit(TIM5, &TIM_ICInitStruct); /*输入通道初始化*/
	TIM_SetCounter(TIM5, CNT_INIT);      /*CNT设初值*/
	TIM_ClearFlag(TIM5,TIM_IT_Update);   /*中断标志清0*/
	TIM_ITConfig(TIM5, TIM_IT_Update, ENABLE); /*中断使能*/
	TIM_Cmd(TIM5,ENABLE);                /*使能CR寄存器*/
	
	/*中断配置*/
//	NVIC_InitStructure.NVIC_IRQChannel=TIM5_IRQn; //定时器5中断
//	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=0x00; //抢占优先级1
//	NVIC_InitStructure.NVIC_IRQChannelSubPriority=0x01; //子优先级1
//	NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
//	NVIC_Init(&NVIC_InitStructure);
} 




/**
* @brief TIM2 通道1通道2 正交编码器-(编码器接口)A15B3--T2：
* @param none
*/
void TIM2_MOTOR3_encoder_init(void)                      
{ 
	GPIO_InitTypeDef GPIO_InitStruct;            /*GPIO*/
	TIM_TimeBaseInitTypeDef  TIM_TimeBaseStruct; /*时基*/
	TIM_ICInitTypeDef TIM_ICInitStruct;          /*输入通道*/
	//NVIC_InitTypeDef NVIC_InitStructure;         /*中断*/
    
   /*GPIO初始化*/    
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE); /*使能GPIO时钟 AHB1*/                    
	GPIO_StructInit(&GPIO_InitStruct);        
	GPIO_InitStruct.GPIO_Pin = GPIO_Pin_15; 
	GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AF;        /*复用功能*/
	GPIO_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;	 /*速度100MHz*/
	GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;   
	GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;        
	GPIO_Init(GPIOA, &GPIO_InitStruct); 
	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE); /*使能GPIO时钟 AHB1*/                    
	GPIO_StructInit(&GPIO_InitStruct);        
	GPIO_InitStruct.GPIO_Pin = GPIO_Pin_3; 
	GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AF;        /*复用功能*/
	GPIO_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;	 /*速度100MHz*/
	GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;   
	GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;        
	GPIO_Init(GPIOB, &GPIO_InitStruct); 
	
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource15,GPIO_AF_TIM2); 
	GPIO_PinAFConfig(GPIOB,GPIO_PinSource3,GPIO_AF_TIM2); 

	/*时基初始化*/
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);   /*使能定时器时钟 APB1*/
	TIM_DeInit(TIM2);  
	TIM_TimeBaseStructInit(&TIM_TimeBaseStruct);    
	TIM_TimeBaseStruct.TIM_Prescaler = ENCODER_TIM_PSC;       /*预分频 */        
	TIM_TimeBaseStruct.TIM_Period = ENCODER_TIM_PERIOD;       /*周期(重装载值)*/
	TIM_TimeBaseStruct.TIM_ClockDivision = TIM_CKD_DIV1;      
	TIM_TimeBaseStruct.TIM_CounterMode = TIM_CounterMode_Up;  /*连续向上计数模式*/  
	TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStruct); 

	/*编码器模式配置：同时捕获通道1与通道2(即4倍频)，极性均为Rising*/
	TIM_EncoderInterfaceConfig(TIM2, TIM_EncoderMode_TI12,TIM_ICPolarity_Rising, TIM_ICPolarity_Rising); 
	TIM_ICStructInit(&TIM_ICInitStruct);        
	TIM_ICInitStruct.TIM_ICFilter = 0;   /*输入通道的滤波参数*/
	TIM_ICInit(TIM2, &TIM_ICInitStruct); /*输入通道初始化*/
	TIM_SetCounter(TIM2, CNT_INIT);      /*CNT设初值*/
	TIM_ClearFlag(TIM2,TIM_IT_Update);   /*中断标志清0*/
	TIM_ITConfig(TIM2, TIM_IT_Update, ENABLE); /*中断使能*/
	TIM_Cmd(TIM2,ENABLE);                /*使能CR寄存器*/
	
	/*中断配置*/
//	NVIC_InitStructure.NVIC_IRQChannel=TIM2_IRQn; //定时器5中断
//	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=0x00; //抢占优先级1
//	NVIC_InitStructure.NVIC_IRQChannelSubPriority=0x01; //子优先级1
//	NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
//	NVIC_Init(&NVIC_InitStructure);

} 


/**
* @brief TIM3 通道1通道2 正交编码器(编码器接口)A0A1--T5
* @param none
*/
void TIM3_MOTOR4_encoder_init(void)                      
{ 
	GPIO_InitTypeDef GPIO_InitStruct;            /*GPIO*/
	TIM_TimeBaseInitTypeDef  TIM_TimeBaseStruct; /*时基*/
	TIM_ICInitTypeDef TIM_ICInitStruct;          /*输入通道*/
    
  /*GPIO初始化*/    
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE); /*使能GPIO时钟 AHB1*/                    
	GPIO_StructInit(&GPIO_InitStruct);        
	GPIO_InitStruct.GPIO_Pin = GPIO_Pin_4| GPIO_Pin_5; 
	GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AF;        /*复用功能*/
	GPIO_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;	 /*速度100MHz*/
	GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;   
	GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;        
	GPIO_Init(GPIOB, &GPIO_InitStruct); 
	
	GPIO_PinAFConfig(GPIOB,GPIO_PinSource4,GPIO_AF_TIM3); 
	GPIO_PinAFConfig(GPIOB,GPIO_PinSource5,GPIO_AF_TIM3); 

	/*时基初始化*/
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);   /*使能定时器时钟 APB1*/
	TIM_DeInit(TIM3);  
	TIM_TimeBaseStructInit(&TIM_TimeBaseStruct);    
	TIM_TimeBaseStruct.TIM_Prescaler = ENCODER_TIM_PSC;       /*预分频 */        
	TIM_TimeBaseStruct.TIM_Period = ENCODER_TIM_PERIOD;       /*周期(重装载值)*/
	TIM_TimeBaseStruct.TIM_ClockDivision = TIM_CKD_DIV1;      
	TIM_TimeBaseStruct.TIM_CounterMode = TIM_CounterMode_Up;  /*连续向上计数模式*/  
	TIM_TimeBaseInit(TIM3, &TIM_TimeBaseStruct); 

	/*编码器模式配置：同时捕获通道1与通道2(即4倍频)，极性均为Rising*/
	TIM_EncoderInterfaceConfig(TIM3, TIM_EncoderMode_TI12,TIM_ICPolarity_Rising, TIM_ICPolarity_Rising); 
	TIM_ICStructInit(&TIM_ICInitStruct);        
	TIM_ICInitStruct.TIM_ICFilter = 0;   /*输入通道的滤波参数*/
	TIM_ICInit(TIM3, &TIM_ICInitStruct); /*输入通道初始化*/
	TIM_SetCounter(TIM3, CNT_INIT);      /*CNT设初值*/
	TIM_ClearFlag(TIM3,TIM_IT_Update);   /*中断标志清0*/
	TIM_ITConfig(TIM3, TIM_IT_Update, ENABLE); /*中断使能*/
	TIM_Cmd(TIM3,ENABLE);                /*使能CR寄存器*/
	
} 

//所有编码器一起配置。
void encoder_config_all(void)
{
	 TIM4_MOTOR1_encoder_init();  //B6--B7  
	 TIM5_MOTOR2_encoder_init();  //A0--A1
	 TIM2_MOTOR3_encoder_init();  //A15--B3
	 TIM3_MOTOR4_encoder_init();  //B4--B5  
}

// 读取定时器计数值
int read_encoder(u8 whichencoder)
{
	int encoderNum = 0;
	if(whichencoder==1)
	{
		encoderNum = (int)((int16_t)(TIM4->CNT)); /*CNT为uint32, 转为int16*/
		TIM_SetCounter(TIM4, CNT_INIT);/*CNT设初值*/	
		return encoderNum;
	}
	else 	if(whichencoder==2)
	{
		encoderNum = (int)((int16_t)(TIM5->CNT)); /*CNT为uint32, 转为int16*/
		TIM_SetCounter(TIM5, CNT_INIT);/*CNT设初值*/	
		return encoderNum;
	}
	else 	if(whichencoder==3)
	{
		encoderNum = (int)((int16_t)(TIM2->CNT)); /*CNT为uint32, 转为int16*/
		TIM_SetCounter(TIM2, CNT_INIT);/*CNT设初值*/	
		return encoderNum;
	}
	else 	if(whichencoder==4)
	{
		encoderNum = (int)((int16_t)(TIM3->CNT)); /*CNT为uint32, 转为int16*/
		TIM_SetCounter(TIM3, CNT_INIT);/*CNT设初值*/	
		return encoderNum;
	}
	else return 0;
}

//获取编码器的方向，大于0一个方向，小于0一个方向
int8_t encoder_get_dir(u8 whichencoder)
{
	int8_t dirtemp=0;
	switch(whichencoder)
	{
		case 1:
		  dirtemp=(TIM4->CR1 & 0x0010)>>4;     //取方向标志位
		  encoderDir[0]=dirtemp;
		break;
		
		case 2:
			dirtemp=(TIM5->CR1 & 0x0010)>>4;     //取方向标志位
			encoderDir[1]=dirtemp;
		break;
			
		case 3:
			 dirtemp=(TIM2->CR1 & 0x0010)>>4;     //取方向标志位
			encoderDir[2]=dirtemp;

		break;
				
		case 4:
			dirtemp=(TIM3->CR1 & 0x0010)>>4;     //取方向标志位
			encoderDir[3]=dirtemp;
		break;
	}
	return dirtemp;
}
//获取编码器的方向，大于0一个方向，小于0一个方向，等于0是停止
int8_t encoder_get_dir_delta(u8 whichencoder)
{
//    int32_t temp;
		int deltaTemp[4];
    deltaTemp[whichencoder-1] = encoderSumCnt[whichencoder-1] - encoderSumCntOld[whichencoder-1];
    encoderSumCntOld[whichencoder-1] = encoderSumCnt[whichencoder-1] ;//更新数据	
		//计算方向
    if(deltaTemp[whichencoder-1]== 0) 
		{//停止	
      encoderDir[whichencoder-1] = 0;
			return 0;
    } 
		else if(deltaTemp[whichencoder-1] > 0) 
		{  //如果差值大于0，那么是正转，
      encoderDir[whichencoder-1] = 1;
			return 1;
    } 
		else 
		{
      encoderDir[whichencoder-1]= -1;//如果差值小于0那么是翻转
			return -1;
    }
}

void encoder_update(void)
{
	//1.获取单位时间内的脉冲数
	Param.UnitTime_Motor1Pluse=read_encoder(1);  
	Param.UnitTime_Motor2Pluse=read_encoder(2);
/*
//					encoderCnt[0]=read_encoder(1);  //获取瞬时走的编码
//					encoderCnt[1]=read_encoder(2);
//					encoderCnt[2]=-read_encoder(3);
//					encoderCnt[3]=read_encoder(4);*/
					
	encoderDir[0]=encoder_get_dir_delta(1); //获取当前的方向
	encoderDir[1]=encoder_get_dir_delta(2);
//	encoderDir[2]=encoder_get_dir_delta(3);
//	encoderDir[3]=encoder_get_dir_delta(4);
	//2.更新累计脉冲数
	Param.Sigma_Motor1Pluse += Param.UnitTime_Motor1Pluse;
	Param.Sigma_Motor2Pluse += Param.UnitTime_Motor2Pluse;
	/*
//					encoderSumCnt[0] +=encoderCnt[0];  //获取小车当前的总的位置
//					encoderSumCnt[1] +=encoderCnt[1];
//					encoderSumCnt[2] +=encoderCnt[2];
//					encoderSumCnt[3] +=encoderCnt[3];*/

	//3.更新小车当前的行驶距离:(周期内累计脉冲/(车轮走一圈产生的脉冲数)*(车轮周长))
	Param.Distance_Motor1Curret = (Param.Sigma_Motor1Pluse/(TOTAL_MOTOR1_RESOLUTION))*(2*3.14*WHEEL_R);
	
}
//计算电机转速（被另一个定时器100ms调用1次）
void calc_motor_rotate_speed(u8 whichencoder)
{
	int encoderNum = 0;
	float rotateSpeed = 0;
	encoderNum = read_encoder(whichencoder);/*读取编码器的值，正负代表旋转方向*/
	/* 转速(1秒钟转多少圈)=单位时间内的计数值/总分辨率*时间系数 */
	/*
	 * TOTAL_MOTORX_RESOLUTION 电机转一圈总的脉冲数
	 */
	switch(whichencoder)
	{
		case 1:	rotateSpeed = (float)encoderNum/TOTAL_MOTOR1_RESOLUTION*10;break;
		case 2:	rotateSpeed = (float)encoderNum/TOTAL_MOTOR2_RESOLUTION*10;break;
		case 3:	rotateSpeed = (float)encoderNum/TOTAL_MOTOR3_RESOLUTION*10;break;
		case 4:	rotateSpeed = (float)encoderNum/TOTAL_MOTOR4_RESOLUTION*10;break;
		default:break;
	}
//	printf("encoder: %d\t speed:%.2f rps\r\n",encoderNum,rotateSpeed);
}
